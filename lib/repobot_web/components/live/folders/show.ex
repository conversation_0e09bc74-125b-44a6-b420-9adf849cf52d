defmodule RepobotWeb.Live.Folders.Show do
  use RepobotWeb, :live_view
  require Logger

  import RepobotWeb.UI.Components
  alias Repobot.{Folders, SourceFiles, Files}

  def mount(%{"id" => id}, _session, socket) do
    folder = Folders.get_folder!(id)
    source_files = get_source_files(socket.assigns)

    # Preload files with content for all repositories (regular and template)
    all_repositories =
      (folder.repositories ++ folder.template_repositories)
      |> Enum.uniq_by(& &1.id)
      |> Files.load_repositories_with_files()

    # Update folder struct with combined, preloaded repositories
    folder = %{folder | repositories: all_repositories}

    # Subscribe to repository file changes if connected
    if connected?(socket) do
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_files")
    end

    socket =
      socket
      |> assign(:folder, folder)
      |> assign(:source_files, source_files)
      |> assign(:page_title, folder.name)
      |> assign(:active_tab, "folder")
      |> assign(:trees_loaded, false)

    {:ok, socket}
  end

  def handle_event("delete_folder", _, socket) do
    case Folders.delete_folder(socket.assigns.folder) do
      {:ok, _folder} ->
        {:noreply,
         socket
         |> put_flash(:info, "Folder deleted successfully")
         |> push_navigate(to: ~p"/repositories")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to delete folder")}
    end
  end

  def handle_event("toggle_star", _, socket) do
    case Folders.toggle_starred(socket.assigns.folder) do
      {:ok, folder} ->
        {:noreply, assign(socket, :folder, folder)}

      {:error, _changeset} ->
        {:noreply, socket}
    end
  end

  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :active_tab, tab)}
  end

  # Forward move_to_global events to the appropriate component
  def handle_event("move_to_global", params, socket) do
    # Forward to the FolderSourceFiles component
    send_update(RepobotWeb.Live.Folders.Components.FolderSourceFiles,
      id: "folder-source-files",
      move_to_global_params: params
    )

    {:noreply, socket}
  end

  # Handle events forwarded from FolderSourceFiles component
  def handle_info({:folder_source_files_event, event_name, params}, socket) do
    case event_name do
      "refresh_folder" ->
        # Refresh the folder data to get updated repository associations
        folder = Folders.get_folder!(socket.assigns.folder.id)
        {:noreply, assign(socket, :folder, folder)}

      "refresh_source_files" ->
        # Refresh source files
        source_files = get_source_files(socket.assigns)
        {:noreply, assign(socket, :source_files, source_files)}

      "flash_message" ->
        # Handle flash messages from component
        %{type: type, message: message} = params
        {:noreply, put_flash(socket, type, message)}
    end
  end

  # Handle events forwarded from GlobalSourceFiles component
  def handle_info({:global_source_files_event, event_name, params}, socket) do
    case event_name do
      "refresh_folder" ->
        # Refresh the folder data to get updated repository associations
        folder = Folders.get_folder!(socket.assigns.folder.id)
        {:noreply, assign(socket, :folder, folder)}

      "refresh_source_files" ->
        # Refresh source files
        source_files = get_source_files(socket.assigns)
        {:noreply, assign(socket, :source_files, source_files)}

      "flash_message" ->
        # Handle flash messages from component
        %{type: type, message: message} = params
        {:noreply, put_flash(socket, type, message)}
    end
  end

  # Handle events forwarded from CommonFiles component
  def handle_info({:common_files_event, event_name, params}, socket) do
    case event_name do
      "refresh_folder" ->
        # Refresh the folder data to get updated repository associations
        folder = Folders.get_folder!(socket.assigns.folder.id)
        {:noreply, assign(socket, :folder, folder)}

      "refresh_source_files" ->
        # Refresh source files
        source_files = get_source_files(socket.assigns)
        {:noreply, assign(socket, :source_files, source_files)}

      "flash_message" ->
        # Handle flash messages from component
        %{type: type, message: message} = params
        {:noreply, put_flash(socket, type, message)}

      "calculate_common_files" ->
        # This is handled by the CommonFiles component itself
        {:noreply, socket}
    end
  end

  # Handle events forwarded from Repositories component
  def handle_info({:repositories_event, event_name, params}, socket) do
    case event_name do
      "trees_loaded" ->
        # Trees are loaded, reload folder data with updated repositories
        folder = Folders.get_folder!(socket.assigns.folder.id)

        socket =
          socket
          |> assign(:folder, folder)
          |> assign(:trees_loaded, true)

        # Only send update to CommonFiles component if it's currently rendered
        if socket.assigns.active_tab == "common" do
          send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
            id: "common-files",
            folder: folder,
            trigger_calculation: true
          )
        end

        {:noreply, socket}

      "content_refreshed" ->
        # Content refresh is complete, this is handled by the main notification handler
        {:noreply, socket}

      "loading_error" ->
        # Handle loading error
        %{reason: reason} = params
        {:noreply, put_flash(socket, :error, "Failed to load repository files: #{reason}")}

      "show_diff" ->
        # Forward show_diff event to the currently active tab component
        case socket.assigns.active_tab do
          "folder" ->
            send_update(RepobotWeb.Live.Folders.Components.FolderSourceFiles,
              id: "folder-source-files",
              show_diff_params: params
            )

          "global" ->
            send_update(RepobotWeb.Live.Folders.Components.GlobalSourceFiles,
              id: "global-source-files",
              show_diff_params: params
            )

          _ ->
            # For other tabs (like "common"), ignore the event
            :ok
        end

        {:noreply, socket}

      _ ->
        # Handle other unknown events - just ignore them
        {:noreply, socket}
    end
  end

  # Handle similarity progress messages from CommonFiles component background processing
  def handle_info({:similarity_progress, progress}, socket) do
    # Forward to CommonFiles component only if it's rendered
    if socket.assigns.active_tab == "common" do
      send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
        id: "common-files",
        similarity_progress: progress
      )
    end

    {:noreply, socket}
  end

  def handle_info({:similarity_complete, files}, socket) do
    # Forward to CommonFiles component only if it's rendered
    if socket.assigns.active_tab == "common" do
      send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
        id: "common-files",
        similarity_complete: files
      )
    end

    {:noreply, socket}
  end

  def handle_info({:similarity_error, reason}, socket) do
    # Forward to CommonFiles component only if it's rendered
    if socket.assigns.active_tab == "common" do
      send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
        id: "common-files",
        similarity_error: reason
      )
    end

    {:noreply, socket}
  end

  # Handle Oban.Notifier messages for CommonFiles component
  def handle_info(
        {:notification, channel,
         %{"event" => "repository_files_progress", "progress" => progress, "message" => _message}},
        socket
      )
      when is_atom(channel) do
    # Check if this is a repository files channel for this user
    if Atom.to_string(channel)
       |> String.starts_with?("repository_files:#{socket.assigns.current_user.id}") do
      # Forward to CommonFiles component only if it's rendered
      if socket.assigns.active_tab == "common" do
        send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
          id: "common-files",
          content_refresh_progress: progress
        )
      end
    end

    {:noreply, socket}
  end

  def handle_info(
        {:notification, channel, %{"event" => "repository_files_complete", "result" => result}},
        socket
      )
      when is_atom(channel) do
    # Check if this is a repository files channel for this user
    if Atom.to_string(channel)
       |> String.starts_with?("repository_files:#{socket.assigns.current_user.id}") do
      # Forward to CommonFiles component only if it's rendered
      if socket.assigns.active_tab == "common" do
        send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
          id: "common-files",
          oban_result: result
        )
      end
    end

    {:noreply, socket}
  end

  # Handle repository files updated events from push webhooks
  def handle_info({:repository_files_updated, repository_id, _metadata}, socket) do
    # Check if any of the repositories in this folder were updated
    folder_repo_ids =
      (socket.assigns.folder.repositories ++ socket.assigns.folder.template_repositories)
      |> Enum.map(& &1.id)
      |> MapSet.new()

    if MapSet.member?(folder_repo_ids, repository_id) do
      # One of our repositories was updated, trigger CommonFiles component recalculation only if it's rendered
      if socket.assigns.active_tab == "common" do
        send_update(RepobotWeb.Live.Folders.Components.CommonFiles,
          id: "common-files",
          trigger_calculation: true
        )
      end

      {:noreply, put_flash(socket, :info, "Repository files updated automatically")}
    else
      {:noreply, socket}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="mb-8">
        <nav class="mb-6">
          <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
            <li>
              <.link navigate={~p"/repositories"} class="link link-hover">
                Repositories
              </.link>
            </li>
            <li>•</li>
            <li class="font-medium text-slate-900">{@folder.name}</li>
          </ol>
        </nav>
        <div class="flex justify-between items-center mb-8">
          <div class="flex items-center gap-2">
            <h1 class="text-2xl font-semibold text-slate-900">{@folder.name}</h1>
            <button
              phx-click="toggle_star"
              class={"text-slate-400 hover:text-slate-500 #{if @folder.starred, do: "text-yellow-400 hover:text-yellow-500"}"}
              aria-label={if @folder.starred, do: "Unstar folder", else: "Star folder"}
            >
              <.icon name="hero-star" class="w-6 h-6" />
            </button>
          </div>
          <div class="flex items-center gap-2">
            <.btn
              href={~p"/folders/#{@folder}/edit"}
              data-phx-link="redirect"
              data-phx-link-state="push"
              class="inline-flex items-center px-3 py-2"
              variant="soft"
            >
              <.icon name="hero-pencil" /> Edit
            </.btn>
            <.btn
              phx-click="delete_folder"
              data-confirm="Are you sure you want to delete this folder? This will unassign all repositories from this folder."
              variant="error"
            >
              <.icon name="hero-trash" /> Delete
            </.btn>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div class="lg:col-span-1">
          <.content>
            <:header>Repositories</:header>
            <:body>
              <.live_component
                module={RepobotWeb.Live.Folders.Components.Repositories}
                id="repositories"
                folder={@folder}
                current_user={@current_user}
              />
            </:body>
          </.content>
        </div>

        <div class="lg:col-span-3">
          <!-- Tab-based interface for file sections -->
          <div class="bg-white rounded-lg shadow-sm border border-slate-200">
            <!-- Tab Navigation -->
            <div class="border-b border-slate-200 bg-slate-50">
              <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button
                  type="button"
                  phx-click="switch_tab"
                  phx-value-tab="folder"
                  class={[
                    "whitespace-nowrap py-4 px-2 border-b-2 font-medium text-base flex items-center cursor-pointer",
                    if(@active_tab == "folder",
                      do: "border-indigo-500 text-indigo-600",
                      else:
                        "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300"
                    )
                  ]}
                >
                  <.icon name="hero-folder" class="w-4 h-4 me-2" /> Folder Source Files
                </button>
                <button
                  type="button"
                  phx-click="switch_tab"
                  phx-value-tab="global"
                  class={[
                    "whitespace-nowrap py-4 px-2 border-b-2 font-medium text-base flex items-center cursor-pointer",
                    if(@active_tab == "global",
                      do: "border-indigo-500 text-indigo-600",
                      else:
                        "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300"
                    )
                  ]}
                >
                  <.icon name="hero-globe-alt" class="w-4 h-4 me-2" /> Global Source Files
                </button>
                <button
                  type="button"
                  phx-click="switch_tab"
                  phx-value-tab="common"
                  class={[
                    "whitespace-nowrap py-4 px-2 border-b-2 font-medium text-base flex items-center cursor-pointer",
                    if(@active_tab == "common",
                      do: "border-indigo-500 text-indigo-600",
                      else:
                        "border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300"
                    )
                  ]}
                >
                  <.icon name="hero-document-duplicate" class="w-4 h-4 me-2" /> Common Files
                </button>
              </nav>
            </div>
            
    <!-- Tab Content -->
            <%= if @active_tab == "folder" do %>
              <.live_component
                module={RepobotWeb.Live.Folders.Components.FolderSourceFiles}
                id="folder-source-files"
                folder={@folder}
                source_files={@source_files}
                current_organization={@current_organization}
              />
            <% end %>

            <%= if @active_tab == "global" do %>
              <.live_component
                module={RepobotWeb.Live.Folders.Components.GlobalSourceFiles}
                id="global-source-files"
                folder={@folder}
                source_files={@source_files}
                current_organization={@current_organization}
              />
            <% end %>

            <%= if @active_tab == "common" do %>
              <.live_component
                module={RepobotWeb.Live.Folders.Components.CommonFiles}
                id="common-files"
                folder={@folder}
                source_files={@source_files}
                current_organization={@current_organization}
                current_user={@current_user}
                trees_loaded={@trees_loaded}
              />
            <% end %>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp get_source_files(%{current_user: user, current_organization: organization}) do
    SourceFiles.list_source_files(user, organization)
  end
end
