defmodule RepobotWeb.Live.Folders.Components.CommonFiles do
  use RepobotWeb, :live_component
  require Logger

  import RepobotWeb.UI.Components
  alias Repobot.{Folders, SourceFiles, Files}
  alias Repobot.Workers.RepositoryFilesWorker
  alias Repobot.Repo

  def mount(socket) do
    socket =
      socket
      |> assign(:common_files, [])
      |> assign(:loading_common_files, true)
      |> assign(:common_files_error, nil)
      |> assign(:content_refresh_progress, nil)
      |> assign(:similarity_progress, nil)
      |> assign(:importing_common_file, nil)
      |> assign(:show_import_dropdown, nil)
      |> assign(:repo_file_contents, %{})
      |> assign(:generating_template_for, nil)
      |> assign(:previewing_file, nil)
      |> assign(:force_refresh_content, false)
      |> assign(:common_files_repo_states, %{})
      |> assign(:loading_timeout_ref, nil)

    {:ok, socket}
  end

  def update(assigns, socket) do
    socket = assign(socket, assigns)

    # Handle updates from parent
    cond do
      Map.has_key?(assigns, :similarity_progress) ->
        {:ok, assign(socket, :similarity_progress, assigns.similarity_progress)}

      Map.has_key?(assigns, :similarity_complete) ->
        {:ok,
         socket
         |> assign(:common_files, assigns.similarity_complete)
         |> assign(:loading_common_files, false)
         |> assign(:similarity_progress, 100)
         |> assign(:content_refresh_progress, nil)
         |> assign(:force_refresh_content, false)}

      Map.has_key?(assigns, :similarity_error) ->
        {:ok,
         socket
         |> assign(:loading_common_files, false)
         |> assign(:common_files_error, assigns.similarity_error)}

      Map.has_key?(assigns, :content_refresh_progress) ->
        {:ok, assign(socket, :content_refresh_progress, assigns.content_refresh_progress)}

      Map.has_key?(assigns, :oban_result) ->
        # Handle Oban completion result
        handle_oban_result(socket, assigns.oban_result)

      Map.get(assigns, :trigger_calculation) ->
        # Trigger calculation directly in the component
        do_calculate_common_files(socket)

      connected?(socket) and socket.assigns.loading_common_files ->
        # Check if trees are already loaded or if repositories have files
        trees_loaded = Map.get(assigns, :trees_loaded, false)
        all_repositories = socket.assigns.folder.repositories

        has_files =
          Enum.any?(all_repositories, fn repo ->
            # Check if files association is loaded and not empty
            case repo.files do
              %Ecto.Association.NotLoaded{} -> false
              files when is_list(files) -> not Enum.empty?(files)
              _ -> false
            end
          end)

        cond do
          has_files ->
            # Repositories have files, calculate common files immediately
            do_calculate_common_files(socket)

          trees_loaded ->
            # Trees are loaded but no files found, show "no common files"
            {:ok,
             socket
             |> assign(:common_files, [])
             |> assign(:loading_common_files, false)
             |> assign(:common_files_error, nil)}

          true ->
            # Trees not loaded yet and no files, wait for trees to be loaded
            # Keep loading state to show "Finding common files..."
            {:ok, socket}
        end

      true ->
        {:ok, socket}
    end
  end

  def render(assigns) do
    ~H"""
    <div>
      <div class="px-6 py-4 border-b border-slate-200 flex justify-between items-center">
        <div class="flex items-center gap-2">
          <.icon name="hero-document-duplicate" class="w-4 h-4 text-slate-400" />
          <p class="text-sm text-slate-600">
            Files that exist in all or some of the repositories in this folder
          </p>
        </div>
        <.btn
          phx-click="refresh_common_files"
          phx-target={@myself}
          disabled={@loading_common_files}
          variant="soft"
          size="xs"
        >
          <%= if @loading_common_files do %>
            <.icon name="hero-arrow-path" class="w-4 h-4 animate-spin mr-2" /> Refreshing...
          <% else %>
            <.icon name="hero-arrow-path" class="w-4 h-4 mr-2" /> Refresh
          <% end %>
        </.btn>
      </div>
      <div class="divide-y divide-slate-200">
        <%= if @loading_common_files do %>
          <div class="px-6 py-8">
            <div class="flex flex-col items-center gap-4">
              <%= if @content_refresh_progress != nil do %>
                <div class="w-full max-w-lg">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-slate-600">Refreshing file content...</span>
                    <span class="text-sm text-slate-600">{@content_refresh_progress}%</span>
                  </div>
                  <div class="w-full bg-slate-200 rounded-full h-2">
                    <div
                      class="bg-indigo-600 h-2 rounded-full transition-all duration-200"
                      style={"width: #{@content_refresh_progress}%"}
                    >
                    </div>
                  </div>
                </div>
              <% end %>

              <%= if @similarity_progress != nil do %>
                <div class="w-full max-w-lg">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-slate-600">
                      Calculating file similarities...
                    </span>
                    <span class="text-sm text-slate-600">{@similarity_progress}%</span>
                  </div>
                  <div class="w-full bg-slate-200 rounded-full h-2">
                    <div
                      class="bg-indigo-600 h-2 rounded-full transition-all duration-200"
                      style={"width: #{@similarity_progress}%"}
                    >
                    </div>
                  </div>
                </div>
              <% end %>

              <%= if @content_refresh_progress == nil and @similarity_progress == nil do %>
                <div class="inline-flex items-center gap-2">
                  <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-600"></div>
                  <span class="text-sm text-slate-600">Finding common files...</span>
                </div>
              <% end %>
            </div>
          </div>
        <% else %>
          <%= if @common_files_error do %>
            <div class="px-6 py-8">
              <div class="flex items-center justify-center gap-2 text-red-600">
                <.icon name="hero-exclamation-circle" class="w-5 h-5" />
                <span>Error loading common files: {@common_files_error}</span>
              </div>
            </div>
          <% else %>
            <%= for file <- @common_files do %>
              <div class="px-6 py-4 group hover:bg-slate-50/50 transition-colors duration-150 border-l-2 border-transparent hover:border-indigo-200">
                <div class="flex items-center justify-between">
                  <div class="flex-1">
                    <div class="flex items-center gap-2">
                      <.icon name="hero-document" class="w-4 h-4 text-slate-400" />
                      <h3 class="text-sm font-semibold text-slate-900">
                        {Path.basename(file["path"])}
                      </h3>
                    </div>
                    <p class="mt-0.5 text-xs text-slate-500 font-mono ml-6">
                      {file["path"]}
                    </p>
                    <%= if Map.has_key?(file, "similarity") do %>
                      <div class="mt-2 ml-6">
                        <div class="flex items-center">
                          <div class="w-32 bg-slate-200 rounded-full h-2 mr-2">
                            <div
                              class={similarity_color_class(file["similarity"])}
                              style={"width: #{file["similarity"]}%"}
                            >
                            </div>
                          </div>
                          <span class="text-xs text-slate-600">
                            {file["similarity"]}% similar
                          </span>
                        </div>
                      </div>
                    <% end %>
                  </div>
                  <div class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
                    <%= if not is_imported?(file, @folder.repositories) and Map.get(file, "similarity", 0) >= 75 do %>
                      <.btn
                        phx-click="generate_template"
                        phx-value-path={file["path"]}
                        phx-target={@myself}
                        phx-disable-with="Generating Template..."
                        data-button="generate-template"
                        data-file-path={file["path"]}
                        variant="soft"
                        size="xs"
                      >
                        <span>Generate Template</span>
                      </.btn>
                    <% end %>
                    <%= if is_imported?(file, @folder.repositories) do %>
                      <span class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-indigo-700">
                        Already Imported
                      </span>
                    <% else %>
                      <div class="relative">
                        <.btn
                          phx-click="show_import_dropdown"
                          phx-value-path={file["path"]}
                          phx-target={@myself}
                          disabled={@importing_common_file == file["path"]}
                          variant="soft"
                          size="xs"
                        >
                          <%= if @importing_common_file == file["path"] do %>
                            Importing...
                          <% else %>
                            Import from...
                          <% end %>
                        </.btn>

                        <%= if @show_import_dropdown == file["path"] do %>
                          <div class="absolute right-0 mt-2 w-72 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                            <div class="py-1" role="menu">
                              <div class="px-4 py-2 text-xs text-slate-500 border-b border-slate-200">
                                Select repository to import from:
                              </div>
                              <%= for {repo_name, data} <- @repo_file_contents do %>
                                <div class="flex items-center justify-between px-4 py-2 text-sm hover:bg-slate-100">
                                  <.btn
                                    phx-click="import_common_file"
                                    phx-value-path={file["path"]}
                                    phx-value-repository={repo_name}
                                    phx-target={@myself}
                                    phx-disable-with="Importing..."
                                    variant="ghost"
                                    class="flex-1 justify-start"
                                    role="menuitem"
                                  >
                                    <div class="flex items-center justify-between w-full">
                                      <span>{repo_name}</span>
                                      <span class="text-xs text-slate-500">
                                        {format_size(data["size"])}
                                      </span>
                                    </div>
                                  </.btn>
                                  <.btn
                                    phx-click="show_file_preview"
                                    phx-value-path={file["path"]}
                                    phx-value-repository={repo_name}
                                    phx-target={@myself}
                                    variant="ghost"
                                    size="xs"
                                    circle
                                    title="Preview file"
                                  >
                                    <.icon name="hero-eye" class="w-4 h-4" />
                                  </.btn>
                                </div>
                              <% end %>
                            </div>
                          </div>

                          <div
                            phx-click="hide_import_dropdown"
                            phx-target={@myself}
                            class="fixed inset-0 z-0"
                          >
                          </div>
                        <% end %>
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
            <%= if Enum.empty?(@common_files) do %>
              <div class="px-6 py-4 text-sm text-slate-500 italic">
                No common files found across all repositories.
              </div>
            <% end %>
          <% end %>
        <% end %>
      </div>
      
    <!-- File Preview Modal -->
      <%= if @previewing_file do %>
        <div class="fixed inset-0 bg-slate-500/75 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col">
            <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-slate-900">
                  Preview File
                </h3>
                <p class="mt-1 text-sm text-slate-500">
                  <span class="font-mono">{@previewing_file.path}</span>
                  from <span class="font-semibold">{@previewing_file.repository}</span>
                </p>
              </div>
              <.btn
                phx-click="hide_file_preview"
                phx-target={@myself}
                variant="ghost"
                size="sm"
                circle
              >
                <.icon name="hero-x-mark" class="w-5 h-5" />
              </.btn>
            </div>
            <div class="p-6 overflow-auto flex-1">
              <pre class="whitespace-pre-wrap font-mono text-sm bg-slate-50 p-4 rounded-lg border border-slate-200 overflow-x-auto"><code><%= @previewing_file.content %></code></pre>
            </div>
            <div class="px-6 py-4 border-t border-slate-200 flex justify-end">
              <.btn phx-click="hide_file_preview" phx-target={@myself} variant="outline">
                Close
              </.btn>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Event handlers
  def handle_event("refresh_common_files", _, socket) do
    socket = assign(socket, :force_refresh_content, true)
    send(self(), {:common_files_event, "calculate_common_files", %{}})
    {:noreply, socket}
  end

  def handle_event("show_import_dropdown", %{"path" => path}, socket) do
    # Get file content from all repositories that have the file
    repo_contents =
      socket.assigns.folder.repositories
      |> Enum.reduce(%{}, fn repo, acc ->
        repo = Files.load_repository_with_files(repo)

        # Find the file in the repository's files
        file_data = Enum.find(repo.files, &(&1.path == path))

        if file_data do
          # Use the stored content if available
          content = Files.get_content(file_data)

          Map.put(acc, repo.full_name, %{
            "content" => content,
            "size" => file_data.size
          })
        else
          acc
        end
      end)

    {:noreply,
     socket
     |> assign(:show_import_dropdown, path)
     |> assign(:repo_file_contents, repo_contents)}
  end

  def handle_event("hide_import_dropdown", _, socket) do
    {:noreply,
     socket
     |> assign(:show_import_dropdown, nil)
     |> assign(:repo_file_contents, %{})}
  end

  def handle_event("import_common_file", %{"path" => path, "repository" => repository}, socket) do
    Logger.info("Importing common file: #{path} from #{repository}")
    socket = assign(socket, :importing_common_file, path)

    repo = Enum.find(socket.assigns.folder.repositories, &(&1.full_name == repository))

    repo =
      repo
      |> Repobot.Repo.preload([:folder, :template_folders])
      |> Files.load_repository_with_files()

    file_data = Enum.find(repo.files, &(&1.path == path))

    if file_data && Files.has_content?(file_data) do
      # Create source file with content and associate with repositories
      attrs = %{
        name: file_data.path,
        content: Files.get_content(file_data),
        target_path: file_data.path,
        organization_id: socket.assigns.current_organization.id,
        source_repository_id: repo.id,
        read_only: repo.template,
        user_id: socket.assigns.current_user.id
      }

      # If importing from a template repository, associate with all repositories
      # from all folders the template is in
      repositories_to_associate =
        if repo.template do
          # Get repositories from both the primary folder and template folders
          folders = [repo.folder | repo.template_folders] |> Enum.reject(&is_nil/1)

          folders
          |> Enum.flat_map(fn folder ->
            Repobot.Repo.preload(folder, :repositories).repositories
          end)
          |> Enum.uniq_by(& &1.id)
        else
          # Keep existing behavior for non-template repositories
          socket.assigns.folder.repositories
        end

      case SourceFiles.import_file(
             attrs,
             repositories_to_associate
           ) do
        {:ok, source_file} ->
          # Associate the source file with the current folder
          {:ok, _} = SourceFiles.add_source_file_to_folder(source_file, socket.assigns.folder)

          # Notify parent to refresh data
          send(self(), {:common_files_event, "refresh_folder", %{}})
          send(self(), {:common_files_event, "refresh_source_files", %{}})

          send(
            self(),
            {:common_files_event, "flash_message",
             %{type: :info, message: "Successfully imported #{path} and added to repositories"}}
          )

          {:noreply,
           socket
           |> assign(:importing_common_file, nil)
           |> assign(:show_import_dropdown, nil)
           |> assign(:repo_file_contents, %{})}

        {:error, reason} ->
          Logger.error("Failed to import file: #{inspect(reason)}")

          send(
            self(),
            {:common_files_event, "flash_message",
             %{type: :error, message: "Failed to import file: #{reason}"}}
          )

          {:noreply,
           socket
           |> assign(:importing_common_file, nil)
           |> assign(:show_import_dropdown, nil)
           |> assign(:repo_file_contents, %{})}
      end
    else
      send(
        self(),
        {:common_files_event, "flash_message",
         %{type: :error, message: "File content not available in database"}}
      )

      {:noreply,
       socket
       |> assign(:importing_common_file, nil)
       |> assign(:show_import_dropdown, nil)
       |> assign(:repo_file_contents, %{})}
    end
  end

  def handle_event("show_file_preview", %{"path" => path, "repository" => repository}, socket) do
    {:noreply,
     socket
     |> assign(:previewing_file, %{
       path: path,
       repository: repository,
       content: get_in(socket.assigns.repo_file_contents, [repository, "content"])
     })}
  end

  def handle_event("hide_file_preview", _, socket) do
    {:noreply, assign(socket, :previewing_file, nil)}
  end

  def handle_event("generate_template", %{"path" => path}, socket) do
    # Find the file with the given path in the common files
    case Enum.find(socket.assigns.common_files, &(&1["path"] == path)) do
      %{"similarity" => similarity} = _file when similarity >= 75 ->
        socket = assign(socket, :generating_template_for, path)

        # Get the two repositories with the highest similarity score
        repo_contents =
          socket.assigns.folder.repositories
          |> Enum.reduce(%{}, fn repo, acc ->
            repo = Files.load_repository_with_files(repo)

            # Find the file in the repository's files
            file_data = Enum.find(repo.files, &(&1.path == path))

            if file_data do
              Map.put(acc, repo.full_name, %{
                "content" => Files.get_content(file_data),
                "path" => file_data.path
              })
            else
              acc
            end
          end)

        # Sort repositories by content similarity and take the top 2
        case find_most_similar_pair(repo_contents) do
          {repo1, data1, repo2, data2} ->
            # Ensure organization settings are preloaded for AI backend selection
            organization = Repo.preload(socket.assigns.current_organization, :settings)

            case Repobot.AI.backend(organization).generate_template(
                   data1["content"],
                   data2["content"],
                   data1["path"],
                   Map.new(Repobot.TemplateContext.available_variables()),
                   organization
                 ) do
              {:ok, template} ->
                # Create a source file with the template
                case SourceFiles.create_source_file(%{
                       name: path,
                       content: template,
                       is_template: true,
                       user_id: socket.assigns.current_user.id,
                       target_path: path,
                       organization_id: socket.assigns.current_user.default_organization_id
                     }) do
                  {:ok, source_file} ->
                    # Associate with repositories and determine tags
                    with {:ok, source_file_with_repos} <-
                           associate_with_repositories(
                             source_file,
                             socket.assigns.folder.repositories
                           ),
                         {:ok, _} <-
                           SourceFiles.add_source_file_to_folder(
                             source_file,
                             socket.assigns.folder
                           ),
                         {:ok, _tagged_source_file} <-
                           SourceFiles.determine_tags(source_file_with_repos) do
                      # Notify parent to refresh source files
                      send(self(), {:common_files_event, "refresh_source_files", %{}})

                      send(
                        self(),
                        {:common_files_event, "flash_message",
                         %{
                           type: :info,
                           message: "Successfully generated template from #{repo1} and #{repo2}"
                         }}
                      )

                      {:noreply, assign(socket, :generating_template_for, nil)}
                    else
                      {:error, _} ->
                        send(
                          self(),
                          {:common_files_event, "flash_message",
                           %{
                             type: :error,
                             message: "Failed to associate template with repositories"
                           }}
                        )

                        {:noreply, assign(socket, :generating_template_for, nil)}
                    end

                  {:error, _changeset} ->
                    send(
                      self(),
                      {:common_files_event, "flash_message",
                       %{type: :error, message: "Failed to create template"}}
                    )

                    {:noreply, assign(socket, :generating_template_for, nil)}
                end

              {:error, reason} ->
                send(
                  self(),
                  {:common_files_event, "flash_message",
                   %{type: :error, message: "Failed to generate template: #{reason}"}}
                )

                {:noreply, assign(socket, :generating_template_for, nil)}
            end

          nil ->
            send(
              self(),
              {:common_files_event, "flash_message",
               %{
                 type: :error,
                 message: "Could not find two similar files to generate template from"
               }}
            )

            {:noreply, assign(socket, :generating_template_for, nil)}
        end

      _ ->
        send(
          self(),
          {:common_files_event, "flash_message",
           %{type: :error, message: "File similarity is too low for template generation"}}
        )

        {:noreply, socket}
    end
  end

  # Handle info messages for background processing
  def handle_info(:calculate_common_files, socket) do
    case do_calculate_common_files(socket) do
      {:ok, updated_socket} -> {:noreply, updated_socket}
    end
  end

  defp start_content_refresh(socket, repositories, common_files) do
    user_id = socket.assigns.current_user.id
    repository_ids = Enum.map(repositories, & &1.id)
    topic = "repository_files:#{user_id}"

    # Extract file paths from common files for content refresh
    file_paths = Enum.map(common_files, & &1["path"])

    # Subscribe to repository files loading events via Oban.Notifier
    channel = String.to_atom(topic)
    Oban.Notifier.listen([channel])

    case RepositoryFilesWorker.enqueue_content_refresh(user_id, repository_ids, topic, file_paths) do
      {:ok, _job} ->
        Logger.info("Enqueued content refresh job for #{length(repository_ids)} repositories")

      {:error, reason} ->
        # Fall back to synchronous content refresh on error
        Logger.warning("Failed to enqueue content refresh job: #{inspect(reason)}")

        Files.RepoTree.refresh_file_content(
          repositories,
          self(),
          socket.assigns.current_user
        )
    end
  end

  defp folder_source_files(source_files, folder) do
    Enum.filter(source_files, fn source_file ->
      Enum.any?(source_file.folders, fn sf_folder -> sf_folder.id == folder.id end)
    end)
  end

  # Handle Oban completion result
  defp handle_oban_result(socket, result) do
    case result do
      %{"status" => "ok", "operation" => "refresh_content", "repository_ids" => _repository_ids} ->
        Logger.info("Repository content refreshed successfully, calculating similarity...")

        # Reload folder and repositories to get the updated file content
        folder = Folders.get_folder!(socket.assigns.folder.id)

        all_repositories =
          (folder.repositories ++ folder.template_repositories)
          |> Enum.uniq_by(& &1.id)
          |> Files.load_repositories_with_files()

        folder = %{folder | repositories: all_repositories}

        socket =
          socket
          # Assign reloaded folder with content
          |> assign(:folder, folder)
          |> assign(:content_refresh_progress, 100)
          # Start similarity progress
          |> assign(:similarity_progress, 0)

        # Use the reloaded repositories list
        Files.calculate_common_files_similarity(
          socket.assigns.common_files,
          all_repositories,
          self()
        )

        {:ok, socket}

      %{"status" => "error", "reason" => reason} ->
        Logger.error("Repository files operation failed: #{reason}")

        {:ok,
         socket
         |> assign(:loading_common_files, false)
         |> assign(:common_files_error, reason)}

      _ ->
        {:ok, socket}
    end
  end

  # Private function to handle common files calculation
  defp do_calculate_common_files(socket) do
    lv_pid = self()
    folder = Folders.get_folder!(socket.assigns.folder.id)

    # Combine regular and template repositories, preload files with content
    all_repositories =
      (folder.repositories ++ folder.template_repositories)
      |> Enum.uniq_by(& &1.id)
      |> Files.load_repositories_with_files()

    # Update folder struct with combined, preloaded repositories
    folder = %{folder | repositories: all_repositories}

    # Get source files that are already imported in this folder to exclude them
    folder_source_files = folder_source_files(socket.assigns.source_files, folder)

    # Find common files, excluding those already imported as source files
    case Files.find_common_files(all_repositories, exclude_source_files: folder_source_files) do
      {:ok, common_files} ->
        # If force refresh is set (manual button click), refresh content first.
        # Otherwise (initial load where files existed), calculate similarity directly.
        socket =
          socket
          |> assign(:folder, folder)
          |> assign(:common_files, common_files)
          # Start loading indicator
          |> assign(:loading_common_files, true)
          # Reset progress bars
          |> assign(:similarity_progress, nil)
          |> assign(:content_refresh_progress, nil)

        if Enum.empty?(common_files) do
          # If no common files, we are done loading
          {:ok, assign(socket, :loading_common_files, false)}
        else
          # Check if any repositories need content refresh (either manual refresh or missing content)
          needs_content_refresh =
            socket.assigns.force_refresh_content == true or
              Enum.any?(all_repositories, fn repo ->
                Enum.any?(repo.files, fn file ->
                  Enum.any?(common_files, fn common_file ->
                    file.path == common_file["path"] and not Files.has_content?(file)
                  end)
                end)
              end)

          if needs_content_refresh do
            # Start content refresh process using RepositoryFilesWorker
            socket = assign(socket, :content_refresh_progress, 0)

            start_content_refresh(socket, all_repositories, common_files)

            {:ok, socket}
          else
            # Files already have content: Calculate similarity directly
            socket = assign(socket, :similarity_progress, 0)
            # Note: We need to pass the parent LiveView PID for similarity calculation messages
            Files.calculate_common_files_similarity(common_files, all_repositories, lv_pid)
            {:ok, socket}
          end
        end

      {:error, reason} ->
        {:ok,
         socket
         |> assign(:loading_common_files, false)
         |> assign(:common_files_error, reason)}
    end
  end

  # Helper functions
  defp is_imported?(file, repositories) do
    result =
      Enum.all?(repositories, fn repo ->
        Enum.any?(repo.source_files || [], &(&1.target_path == file["path"]))
      end)

    result
  end

  defp similarity_color_class(similarity) when similarity >= 90,
    do: "bg-green-500 h-2 rounded-full"

  defp similarity_color_class(similarity) when similarity >= 70,
    do: "bg-yellow-500 h-2 rounded-full"

  defp similarity_color_class(_), do: "bg-red-500 h-2 rounded-full"

  defp find_most_similar_pair(repo_contents), do: Files.find_most_similar_pair(repo_contents)

  defp format_size(size) when is_integer(size) do
    cond do
      size < 1024 -> "#{size} B"
      size < 1024 * 1024 -> "#{Float.round(size / 1024, 1)} KB"
      size < 1024 * 1024 * 1024 -> "#{Float.round(size / 1024 / 1024, 1)} MB"
      true -> "#{Float.round(size / 1024 / 1024 / 1024, 1)} GB"
    end
  end

  defp format_size(_), do: "Unknown size"

  defp associate_with_repositories(source_file, repositories) do
    results =
      Enum.map(repositories, fn repository ->
        Repobot.Repositories.add_source_file(repository, source_file)
      end)

    case Enum.split_with(results, fn
           {:ok, _} -> true
           _ -> false
         end) do
      {_successes, []} -> {:ok, source_file}
      {_, _failures} -> {:error, :association}
    end
  end
end
