defmodule RepobotWeb.Live.Shared.DiffModal do
  @moduledoc """
  A reusable diff modal component following Phoenix 1.7 modal guidelines.

  This component encapsulates all diff modal functionality including:
  - State management for modal visibility and data
  - Event handling for showing/hiding and repository comparison
  - Rendering logic with proper modal structure
  - Integration with the Diff JavaScript hook

  ## Usage

      <.live_component
        module={RepobotWeb.Live.Shared.DiffModal}
        id="diff-modal"
        show={@show_diff_modal}
        source_file={@diff_source_file}
        repository={@diff_repository}
        available_repositories={@available_repositories}
        on_hide={JS.push("hide_diff_modal", target: @myself)}
      />

  ## Events

  The component sends events to its parent:
  - `diff_modal_hidden` - when the modal is closed
  - `diff_repository_changed` - when a different repository is selected for comparison
  """
  use RepobotWeb, :live_component
  require Logger

  import RepobotWeb.UI.Components
  alias Repobot.{SourceFiles, Files}
  alias Repobot.Repo
  alias Phoenix.LiveView.JS

  @doc """
  Component attributes:
  - show: boolean - whether to show the modal
  - source_file: struct - the source file being compared
  - repository: struct - the current repository for comparison
  - folder: struct - the folder containing repositories for comparison
  - on_hide: JS command - JavaScript command to execute when hiding the modal
  """
  attr :show, :boolean, default: false
  attr :source_file, :any, default: nil
  attr :repository, :any, default: nil
  attr :folder, :any, default: nil
  attr :on_hide, :any, default: nil

  def render(assigns) do
    ~H"""
    <div id={"diff-modal-#{@id}"} class={unless @show, do: "hidden"}>
      <div
        class="fixed inset-0 bg-slate-500/75 flex items-center justify-center z-50"
        phx-window-keydown={@on_hide || JS.push("hide_diff_modal", target: @myself)}
        phx-key="escape"
      >
        <div class="bg-white rounded-lg shadow-xl max-w-7xl w-full mx-4 max-h-[90vh] flex flex-col">
          <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
            <div class="flex items-center gap-4">
              <h3 class="text-lg font-medium text-slate-900">
                File diff for {@repository && @repository.full_name}
              </h3>
              <%= if @folder && @source_file && @repository do %>
                <% available_repositories =
                  get_available_repositories(@folder, @source_file, @repository) %>
                <%= if not Enum.empty?(available_repositories) do %>
                  <div class="relative">
                    <form
                      phx-change="change_diff_repository"
                      phx-target={@myself}
                      class="flex items-center"
                    >
                      <select name="repository" class="select">
                        <option value="" disabled selected>Compare with...</option>
                        <%= for repo <- available_repositories do %>
                          <option value={repo}>{repo}</option>
                        <% end %>
                      </select>
                    </form>
                  </div>
                <% end %>
              <% end %>
            </div>
            <.btn
              phx-click={@on_hide || JS.push("hide_diff_modal", target: @myself)}
              variant="ghost"
              size="sm"
              circle
            >
              <.icon name="hero-x-mark" class="w-5 h-5" />
            </.btn>
          </div>
          <div class="p-6 overflow-auto flex-1" id={"diff-container-#{@id}"} phx-hook="Diff"></div>
        </div>
      </div>
    </div>
    """
  end

  def mount(socket) do
    {:ok, socket}
  end

  def update(assigns, socket) do
    socket = assign(socket, assigns)

    # If the modal is being shown and we have the necessary data, render the diff
    if assigns[:show] && assigns[:source_file] && assigns[:repository] do
      socket = render_diff(socket)
      {:ok, socket}
    else
      {:ok, socket}
    end
  end

  def handle_event("hide_diff_modal", _params, socket) do
    # Hide the modal by updating its state
    socket = assign(socket, :show, false)
    {:noreply, socket}
  end

  def handle_event("change_diff_repository", %{"repository" => repository}, socket) do
    [owner, repo] = String.split(repository, "/")

    # Get repository and its files from database
    repository =
      Repobot.Repository
      |> Repo.get_by!(owner: owner, name: repo)
      |> Repobot.Repo.preload([:folder])
      |> Files.load_repository_with_files()

    # Update the socket with the new repository and render the diff
    socket =
      socket
      |> assign(:repository, repository)
      |> render_diff()

    {:noreply, socket}
  end

  defp render_diff(socket) do
    source_file = socket.assigns.source_file
    repository = socket.assigns.repository

    # Get the file content from our database
    repo_content =
      repository.files
      |> Enum.find(&(&1.path == source_file.target_path))
      |> case do
        file when not is_nil(file) -> Files.get_content(file)
        _ -> ""
      end

    # Render the source file template for this repository if it's a template
    rendered_content =
      if source_file.is_template do
        case SourceFiles.render_template_for_repository(source_file, repository) do
          {:ok, content} -> content
          {:error, _reason} -> Files.get_content(source_file)
        end
      else
        Files.get_content(source_file)
      end

    # Push the diff rendering event to the JavaScript hook
    socket
    |> push_event("render_diff", %{source: rendered_content, target: repo_content || ""})
  end

  defp get_available_repositories(folder, source_file, current_repository) do
    folder.repositories
    |> Enum.reject(& &1.template)
    |> Enum.filter(fn repo ->
      # Include repositories that have this source file and are not the current repository
      repo.id != current_repository.id and
        Enum.any?(repo.source_files || [], &(&1.id == source_file.id))
    end)
    |> Enum.map(& &1.full_name)
  end

  @doc """
  Helper function to prepare diff modal data for showing.

  This function loads the repository data and prepares all the necessary assigns
  for the diff modal component.
  """
  def prepare_diff_data(source_file_id, repository_name, folder) do
    [owner, repo] = String.split(repository_name, "/")
    source_file = SourceFiles.get_source_file!(source_file_id)

    # Get repository and its files from database
    repository =
      Repobot.Repository
      |> Repo.get_by!(owner: owner, name: repo)
      |> Repobot.Repo.preload([:folder])
      |> Files.load_repository_with_files()

    %{
      source_file: source_file,
      repository: repository,
      folder: folder
    }
  end
end
